const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');

/**
 * Enhanced Playwright Crawler with comprehensive error handling and state management
 */
class EnhancedPlaywrightCrawler {
    constructor(config = {}) {
        this.config = {
            headless: config.headless !== false,
            timeout: config.timeout || 100000,
            pageTimeout: config.pageTimeout || 100000,
            maxInstances: config.maxInstances || 5,
            minDelay: config.minDelay || 1000,
            maxDelay: config.maxDelay || 3000,
            maxRetries: config.maxRetries || 3,
            retryDelay: config.retryDelay || 5000,
            requestTimeout: config.requestTimeout || 30000,
            outputDir: config.outputDir || './output',
            userAgent: config.userAgent || 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            ...config
        };

        this.logger = config.logger;
        this.errorManager = config.errorManager;
        this.tokenManager = config.tokenManager;

        // Browser management
        this.browser = null;
        this.contexts = new Map();
        this.pages = new Map();
        this.activeRequests = new Set();
        
        // State tracking
        this.isInitialized = false;
        this.isShuttingDown = false;
        this.requestCount = 0;
        this.successCount = 0;
        this.errorCount = 0;

        // Performance metrics
        this.metrics = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0,
            responseTimes: []
        };
    }

    /**
     * Initialize the crawler
     */
    async initialize() {
        if (this.isInitialized) return;

        try {
            this.logger?.info('🚀 Initializing Enhanced Playwright Crawler');

            // Ensure output directory exists
            if (!fs.existsSync(this.config.outputDir)) {
                fs.mkdirSync(this.config.outputDir, { recursive: true });
            }

            // Launch browser
            await this.launchBrowser();
            
            this.isInitialized = true;
            this.logger?.info('✅ Enhanced Playwright Crawler initialized');

        } catch (error) {
            const enhancedError = this.errorManager?.createEnhancedError(error, {
                operation: 'crawler_initialization'
            }) || error;
            
            this.logger?.error('❌ Failed to initialize crawler', { error: enhancedError.message });
            throw enhancedError;
        }
    }

    /**
     * Launch browser with enhanced configuration
     */
    async launchBrowser() {
        const launchOptions = {
            headless: this.config.headless,
            timeout: this.config.timeout,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--no-zygote',
                '--disable-gpu',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding'
            ]
        };

        this.browser = await chromium.launch(launchOptions);
        
        this.logger?.info('🌐 Browser launched', {
            headless: this.config.headless,
            version: await this.browser.version()
        });

        // Setup browser event handlers
        this.browser.on('disconnected', () => {
            this.logger?.warn('🔌 Browser disconnected');
            this.browser = null;
        });
    }

    /**
     * Create new browser context with enhanced settings
     */
    async createContext(contextId = null) {
        if (!this.browser) {
            await this.launchBrowser();
        }

        const actualContextId = contextId || `context_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;

        const context = await this.browser.newContext({
            userAgent: this.config.userAgent,
            viewport: { width: 1920, height: 1080 },
            ignoreHTTPSErrors: true,
            timeout: this.config.pageTimeout,
            extraHTTPHeaders: {
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'Accept-Encoding': 'gzip, deflate',
                'DNT': '1',
                'Connection': 'keep-alive',
                'Upgrade-Insecure-Requests': '1'
            }
        });

        // Setup context event handlers
        context.on('page', (page) => {
            this.setupPageHandlers(page);
        });

        this.contexts.set(actualContextId, context);
        
        this.logger?.debug('📄 Browser context created', { contextId: actualContextId });
        return { context, contextId: actualContextId };
    }

    /**
     * Setup page event handlers
     */
    setupPageHandlers(page) {
        page.on('console', (msg) => {
            if (msg.type() === 'error') {
                this.logger?.debug('🖥️ Page console error', { message: msg.text() });
            }
        });

        page.on('pageerror', (error) => {
            this.logger?.debug('🖥️ Page error', { error: error.message });
        });

        page.on('requestfailed', (request) => {
            this.logger?.debug('🌐 Request failed', { 
                url: request.url(), 
                failure: request.failure()?.errorText 
            });
        });
    }

    /**
     * Create new page with enhanced configuration
     */
    async createPage(contextId = null) {
        let context;
        let actualContextId = contextId;

        if (contextId && this.contexts.has(contextId)) {
            context = this.contexts.get(contextId);
        } else {
            const result = await this.createContext();
            context = result.context;
            actualContextId = result.contextId;
        }

        const page = await context.newPage();
        const pageId = `page_${Date.now()}_${Math.random().toString(36).substr(2, 5)}`;

        // Configure page settings
        await page.setDefaultTimeout(this.config.pageTimeout);
        await page.setDefaultNavigationTimeout(this.config.pageTimeout);

        this.pages.set(pageId, { page, contextId: actualContextId });
        
        this.logger?.debug('📃 Page created', { pageId, contextId: actualContextId });
        return { page, pageId, contextId: actualContextId };
    }

    /**
     * Scrape data with comprehensive error handling and retry logic
     * Enhanced with detailed boundary checking and page validation from original crawler
     */
    async scrapeData(parameters, token = null) {
        const operationId = this.logger?.startOperation('scrape_data', { parameters });
        const startTime = Date.now();

        try {
            // Validate inputs
            if (!parameters) {
                throw new Error('Parameters are required for scraping');
            }

            // Get or assign token
            const actualToken = token || await this.getAvailableToken();
            if (!actualToken) {
                throw new Error('No valid token available for scraping');
            }

            // Create page for scraping
            const { page, pageId, contextId } = await this.createPage();

            try {
                // Set authentication token
                await this.setAuthenticationToken(page, actualToken);

                // Enhanced scraping with detailed page processing
                const scrapingResult = await this.performEnhancedScraping(page, parameters, actualToken);

                const duration = Date.now() - startTime;
                this.updateMetrics(true, duration);

                // Record token usage
                if (this.tokenManager) {
                    this.tokenManager.recordTokenUsage(actualToken.tokenId, true, duration);
                }

                this.logger?.completeOperation('scrape_data', operationId, {
                    parameters,
                    duration,
                    savedPath: scrapingResult.savedPath,
                    dataSize: scrapingResult.totalCount || 0
                });

                return {
                    success: true,
                    data: scrapingResult.data,
                    savedPath: scrapingResult.savedPath,
                    duration: duration,
                    token: actualToken,
                    totalCount: scrapingResult.totalCount,
                    pagesProcessed: scrapingResult.pagesProcessed
                };

            } finally {
                // Cleanup page
                await this.closePage(pageId);
            }

        } catch (error) {
            const duration = Date.now() - startTime;
            this.updateMetrics(false, duration);

            const enhancedError = this.errorManager?.createEnhancedError(error, {
                operation: 'scrape_data',
                parameters: parameters,
                token: token?.tokenId
            }) || error;

            // Record token usage failure
            if (token && this.tokenManager) {
                this.tokenManager.recordTokenUsage(token.tokenId, false, duration);
            }

            this.logger?.failOperation('scrape_data', operationId, enhancedError, {
                parameters,
                duration
            });

            // Determine if error is retryable
            const recoveryStrategy = this.errorManager?.getRecoveryStrategy(enhancedError);
            enhancedError.retryable = recoveryStrategy?.retryable || false;

            throw enhancedError;
        }
    }

    /**
     * Enhanced scraping method with detailed page processing and boundary checking
     * Integrated from original PlaywrightCrawler.js logic
     */
    async performEnhancedScraping(page, parameters, token) {
        const baseURL = 'https://zj.stzy.com/create-paper/chapter';
        let currentPage = 1;
        let maxPages = this.config.maxPages || 50; // Default max pages
        let totalCount = 0;
        let pagesProcessed = 0;
        let allData = [];
        let isSettingsConfigured = false;

        // Create output directory for this parameter combination
        const pathInfo = this.generateOutputPath(parameters);
        const outputPath = pathInfo.encodedPath;

        if (!fs.existsSync(outputPath)) {
            fs.mkdirSync(outputPath, { recursive: true });
        }

        this.logger?.info(`📂 Output directory created`, {
            encodedPath: pathInfo.encodedPath,
            originalPath: pathInfo.originalPath,
            parameters: {
                studyPhaseName: parameters.studyPhaseName,
                subjectName: parameters.subjectName,
                textbookVersionName: parameters.textbookVersionName,
                ceciName: parameters.ceciName,
                catalogName: parameters.catalogName
            }
        });

        while (currentPage <= maxPages) {
            try {
                this.logger?.debug(`🚀 Processing page ${currentPage}/${maxPages}`, { parameters, currentPage, maxPages });

                // Check if page file already exists
                const pageFilePath = path.join(outputPath, `${currentPage}.json`);
                if (fs.existsSync(pageFilePath)) {
                    this.logger?.debug(`⏭️ Page ${currentPage} already exists, skipping`);
                    currentPage++;
                    pagesProcessed++;
                    continue;
                }

                // Navigate and configure on first page or if settings not configured
                if (currentPage === 1 || !isSettingsConfigured) {
                    await this.navigateAndConfigure(page, baseURL, parameters);
                    await this.configurePageSettings(page, parameters);
                    isSettingsConfigured = true;

                    // Get actual max pages from pagination after first page setup
                    try {
                        const actualMaxPages = await this.getMaxPageFromPagination(page);
                        if (actualMaxPages > 0) {
                            maxPages = actualMaxPages;
                            this.logger?.info(`📄 Updated max pages to: ${maxPages}`);
                        }
                    } catch (error) {
                        this.logger?.warn(`⚠️ Failed to get max pages, using default: ${error.message}`);
                    }
                }

                // Navigate to specific page if not the first page
                if (currentPage > 1) {
                    const paginationResult = await this.handlePagination(page, currentPage);
                    if (!paginationResult.success) {
                        this.logger?.error(`❌ Failed to navigate to page ${currentPage}: ${paginationResult.reason}`);
                        break;
                    }
                }

                // Check for loading state and handle timeouts
                const loadingCheck = await this.checkLoadingState(page);
                if (loadingCheck.needAction) {
                    const timeoutResult = await this.handleLoadingTimeout(page, `Page ${currentPage}`);
                    if (timeoutResult.needSwitchToken) {
                        throw new Error(`Loading timeout on page ${currentPage}: ${timeoutResult.reason}`);
                    }
                }

                // Validate page elements
                const elementCheck = await this.checkPageElementsWithRetry(page, `Page ${currentPage}`);
                if (!elementCheck.valid) {
                    if (elementCheck.needSwitchToken) {
                        throw new Error(`Page validation failed on page ${currentPage}: ${elementCheck.reason}`);
                    }
                }

                // Check for "no content found" message (boundary condition)
                const noContentCheck = await this.checkNoContentFound(page);
                if (noContentCheck.noContentFound) {
                    this.logger?.info(`✅ Reached end of content on page ${currentPage}`, {
                        reason: noContentCheck.reason,
                        message: noContentCheck.message
                    });
                    break;
                }

                // Set up network listening for API response capture
                const responsePromise = this.setupNetworkListening(page, currentPage);

                // Wait for API response
                const apiResponse = await responsePromise;

                // Check for timeout
                if (apiResponse.timeout) {
                    this.logger?.warn(`⏰ Page ${currentPage} response timeout`);
                    break;
                }

                // Check for token ban
                if (apiResponse.tokenBanned) {
                    this.logger?.error(`🚫 Token banned detected on page ${currentPage}`);

                    // Mark token as permanently invalid
                    if (actualToken && this.tokenManager) {
                        this.tokenManager.markTokenPermanentlyInvalid(actualToken.tokenId, 'API returned 403 error');
                    }

                    throw new Error('Token banned');
                }

                // Process API response data
                if (apiResponse.data && apiResponse.data.data && apiResponse.data.data.list) {
                    const list = apiResponse.data.data.list;

                    if (list.length > 0) {
                        // Save page data with complete information like original crawler
                        await this.savePageData(currentPage, list, apiResponse.data, apiResponse.requestData, pathInfo, parameters);

                        allData = allData.concat(list);
                        totalCount += list.length;
                        pagesProcessed++;

                        this.logger?.debug(`✅ Page ${currentPage} processed`, {
                            itemCount: list.length,
                            totalCount
                        });
                    } else {
                        this.logger?.warn(`⚠️ No data found on page ${currentPage}`);
                        break;
                    }
                } else {
                    this.logger?.warn(`⚠️ Invalid response format on page ${currentPage}`);
                    break;
                }

                currentPage++;

                // Add intelligent delay between pages
                await this.intelligentDelay();

            } catch (error) {
                this.logger?.error(`❌ Error processing page ${currentPage}`, {
                    error: error.message,
                    parameters
                });
                throw error;
            }
        }

        return {
            data: allData,
            savedPath: outputPath,
            totalCount,
            pagesProcessed
        };
    }

    /**
     * Get available token from token manager
     */
    async getAvailableToken() {
        if (!this.tokenManager) {
            throw new Error('Token manager not configured');
        }

        const tokenInfo = this.tokenManager.getAvailableToken();
        if (!tokenInfo) {
            throw new Error('No available tokens');
        }

        // Assign token to this instance
        this.tokenManager.assignToken(tokenInfo.tokenId);

        // Store current token for later reference
        this.currentToken = {
            tokenId: tokenInfo.tokenId,
            token: tokenInfo.token.token,
            phone: tokenInfo.token.phone
        };

        return this.currentToken;
    }

    /**
     * Get current token
     */
    getCurrentToken() {
        return this.currentToken;
    }

    /**
     * Set authentication token on page
     */
    async setAuthenticationToken(page, tokenInfo) {
        try {
            // Set token in localStorage or cookies as needed
            await page.addInitScript((token) => {
                localStorage.setItem('authToken', token);
                localStorage.setItem('userToken', token);
            }, tokenInfo.token);

            // Set authorization header for requests
            await page.setExtraHTTPHeaders({
                'Authorization': `Bearer ${tokenInfo.token}`,
                'X-Auth-Token': tokenInfo.token
            });

            this.logger?.debug('🔐 Authentication token set', { tokenId: tokenInfo.tokenId });

        } catch (error) {
            throw new Error(`Failed to set authentication token: ${error.message}`);
        }
    }

    /**
     * Navigate to target page with retry logic
     */
    async navigateToPage(page, parameters) {
        const baseURL = this.config.baseURL || 'https://zj.stzy.com/create-paper/chapter';
        const targetURL = this.buildTargetURL(baseURL, parameters);

        let lastError;
        for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
            try {
                this.logger?.debug('🧭 Navigating to page', { url: targetURL, attempt });

                const response = await page.goto(targetURL, {
                    waitUntil: 'networkidle',
                    timeout: this.config.requestTimeout
                });

                if (!response.ok()) {
                    throw new Error(`HTTP ${response.status()}: ${response.statusText()}`);
                }

                // Wait for page to be ready
                await this.waitForPageReady(page);

                this.logger?.debug('✅ Navigation successful', { url: targetURL });
                return response;

            } catch (error) {
                lastError = error;
                this.logger?.warn(`❌ Navigation attempt ${attempt} failed`, { 
                    url: targetURL, 
                    error: error.message 
                });

                if (attempt < this.config.maxRetries) {
                    const delay = this.config.retryDelay * attempt;
                    await this.sleep(delay);
                }
            }
        }

        throw new Error(`Navigation failed after ${this.config.maxRetries} attempts: ${lastError.message}`);
    }

    /**
     * Build target URL from parameters
     */
    buildTargetURL(baseURL, parameters) {
        const queryParams = new URLSearchParams();
        
        if (parameters.studyPhaseCode) queryParams.set('studyPhaseCode', parameters.studyPhaseCode);
        if (parameters.subjectCode) queryParams.set('subjectCode', parameters.subjectCode);
        if (parameters.textbookVersionCode) queryParams.set('textbookVersionCode', parameters.textbookVersionCode);
        if (parameters.catalogCode) queryParams.set('catalogCode', parameters.catalogCode);
        if (parameters.chapterCode) queryParams.set('chapterCode', parameters.chapterCode);
        if (parameters.sectionCode) queryParams.set('sectionCode', parameters.sectionCode);

        return `${baseURL}?${queryParams.toString()}`;
    }

    /**
     * Wait for page to be ready for scraping
     */
    async waitForPageReady(page) {
        try {
            // Wait for basic page elements
            await page.waitForLoadState('networkidle', { timeout: 10000 });
            
            // Wait for specific elements that indicate page is ready
            await page.waitForSelector('body', { timeout: 5000 });
            
            // Additional wait for dynamic content
            await this.sleep(2000);

        } catch (error) {
            this.logger?.warn('⏰ Page ready wait timeout', { error: error.message });
            // Continue anyway, as some pages might not have expected elements
        }
    }

    /**
     * Extract data from page
     */
    async extractData(page, parameters) {
        try {
            this.logger?.debug('📊 Extracting data from page');

            // Wait for content to load
            await this.sleep(this.getRandomDelay());

            // Extract page content
            const pageData = await page.evaluate(() => {
                return {
                    title: document.title,
                    url: window.location.href,
                    timestamp: new Date().toISOString(),
                    content: document.body.innerHTML,
                    textContent: document.body.textContent,
                    metadata: {
                        charset: document.characterSet,
                        readyState: document.readyState,
                        referrer: document.referrer
                    }
                };
            });

            // Add parameter information
            pageData.parameters = parameters;
            pageData.extractedAt = new Date().toISOString();

            this.logger?.debug('✅ Data extraction completed', { 
                contentSize: pageData.content?.length || 0,
                textSize: pageData.textContent?.length || 0
            });

            return pageData;

        } catch (error) {
            throw new Error(`Data extraction failed: ${error.message}`);
        }
    }

    /**
     * Save extracted results to file
     */
    async saveResults(data, parameters) {
        try {
            const filename = this.generateFilename(parameters);
            const filepath = path.join(this.config.outputDir, filename);

            // Ensure directory exists
            const dir = path.dirname(filepath);
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }

            // Save data as JSON
            fs.writeFileSync(filepath, JSON.stringify(data, null, 2), 'utf8');

            this.logger?.debug('💾 Results saved', { filepath, size: data.content?.length || 0 });
            return filepath;

        } catch (error) {
            throw new Error(`Failed to save results: ${error.message}`);
        }
    }

    /**
     * Generate filename for results
     */
    generateFilename(parameters) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const paramKey = [
            parameters.studyPhaseCode,
            parameters.subjectCode,
            parameters.textbookVersionCode,
            parameters.catalogCode,
            parameters.chapterCode,
            parameters.sectionCode
        ].filter(Boolean).join('_');

        return `scrape_${paramKey}_${timestamp}.json`;
    }

    /**
     * Generate output path for parameter combination
     */
    generateOutputPath(parameters) {
        const sanitize = (name) => {
            if (!name) return 'unknown';
            return name.replace(/[<>:"/\\|?*\n\r\t]/g, '_').trim();
        };

        const pathComponents = [
            sanitize(parameters.studyPhaseName),
            sanitize(parameters.subjectName),
            sanitize(parameters.textbookVersionName),
            sanitize(parameters.ceciName),
            sanitize(parameters.catalogName)
        ];

        return path.join(this.config.outputDir, ...pathComponents);
    }

    /**
     * Navigate to page and configure settings
     */
    async navigateAndConfigure(page, baseURL, parameters) {
        this.logger?.debug('🔗 Navigating to base URL and configuring settings');

        await page.goto(baseURL, {
            waitUntil: 'networkidle',
            timeout: this.config.pageTimeout || 100000
        });

        // Wait for page to stabilize
        await page.waitForTimeout(2000);

        // Configure page settings based on parameters
        await this.configurePageSettings(page, parameters);
    }

    /**
     * Configure page settings (placeholder - implement based on original logic)
     */
    async configurePageSettings(page, parameters) {
        // This would contain the detailed setting configuration logic
        // from the original crawler's clickSettingElements method
        this.logger?.debug('⚙️ Configuring page settings', { parameters });

        // Add implementation based on original crawler logic
        // For now, just wait to ensure page is ready
        await page.waitForTimeout(1000);
    }

    /**
     * Navigate to specific page number
     */
    async navigateToPage(page, pageNumber) {
        this.logger?.debug(`📄 Navigating to page ${pageNumber}`);

        try {
            // Look for page input field and navigate
            const pageInput = page.locator('input.pagination_jump_page_input');
            if (await pageInput.count() > 0) {
                await pageInput.clear();
                await pageInput.fill(pageNumber.toString());
                await page.keyboard.press('Enter');
                await page.waitForTimeout(2000);
            }
        } catch (error) {
            this.logger?.warn(`Failed to navigate to page ${pageNumber}`, { error: error.message });
        }
    }

    /**
     * Extract data from current page
     */
    async extractPageData(page, pageNumber) {
        try {
            this.logger?.debug(`📊 Extracting data from page ${pageNumber}`);

            // Wait for content to load
            await page.waitForTimeout(2000);

            // Extract data based on page structure
            // This is a placeholder - implement based on actual page structure
            const data = await page.evaluate(() => {
                // Add actual data extraction logic here
                return [];
            });

            return data;

        } catch (error) {
            this.logger?.error(`Failed to extract data from page ${pageNumber}`, { error: error.message });
            return [];
        }
    }

    /**
     * Intelligent delay with randomization
     */
    async intelligentDelay() {
        const delay = this.getRandomDelay();
        this.logger?.debug(`⏱️ Applying intelligent delay: ${delay}ms`);
        await this.sleep(delay);
    }

    /**
     * Check for "no content found" message (boundary condition)
     * Integrated from original PlaywrightCrawler.js
     */
    async checkNoContentFound(page) {
        try {
            this.logger?.debug('🔍 Checking for "no content found" message');

            // Wait for network requests to complete
            await page.waitForLoadState('networkidle', { timeout: 30000 });
            await page.waitForTimeout(2000);

            // Look for img elements with class="w120 h120"
            const imgElements = await page.locator('img.w120.h120').all();

            if (imgElements.length === 0) {
                return { noContentFound: false, reason: 'No target img elements found' };
            }

            this.logger?.debug(`🖼️ Found ${imgElements.length} target img elements`);

            // Check each img element's siblings for the "no content" message
            for (let i = 0; i < imgElements.length; i++) {
                const img = imgElements[i];

                try {
                    const parent = img.locator('..');
                    const siblingPs = await parent.locator('p').all();

                    for (const p of siblingPs) {
                        const textContent = await p.textContent();
                        if (textContent && textContent.includes('没有查询到您想要的内容')) {
                            const isVisible = await p.isVisible();

                            this.logger?.debug(`🎯 Found "no content" message`, {
                                visible: isVisible,
                                text: textContent.trim()
                            });

                            if (isVisible) {
                                return {
                                    noContentFound: true,
                                    reason: 'No content message displayed',
                                    message: textContent.trim(),
                                    isVisible: isVisible
                                };
                            }
                        }
                    }
                } catch (error) {
                    this.logger?.warn(`Error checking img element ${i + 1}`, { error: error.message });
                }
            }

            return { noContentFound: false, reason: 'No "no content" message found' };

        } catch (error) {
            this.logger?.warn('Error checking for no content message', { error: error.message });
            return { noContentFound: false, reason: `Check error: ${error.message}` };
        }
    }

    /**
     * Check page elements with retry logic
     * Integrated from original PlaywrightCrawler.js
     */
    async checkPageElementsWithRetry(page, pageContext, maxRetries = 3) {
        let lastResult = null;

        this.logger?.debug(`🔍 Checking page elements (${pageContext})`);

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            this.logger?.debug(`📍 Element check attempt ${attempt}/${maxRetries}`);

            const result = await this.checkPageElements(page);
            lastResult = result;

            if (result.valid) {
                if (attempt > 1) {
                    this.logger?.info(`✅ Page elements valid on attempt ${attempt}`);
                } else {
                    this.logger?.debug(`✅ Page elements check passed`);
                }
                return result;
            }

            this.logger?.warn(`❌ Element check failed (attempt ${attempt}): ${result.reason}`);

            // Check for critical failures that require token switch
            if (result.isLoginRedirect || result.isTokenError) {
                this.logger?.error(`🚫 Critical failure detected, need token switch`);

                // Mark current token as permanently invalid
                const currentToken = this.getCurrentToken();
                if (currentToken && this.tokenManager) {
                    const reason = result.isLoginRedirect ? 'Login redirect detected' : 'Token error detected';
                    this.tokenManager.markTokenPermanentlyInvalid(currentToken.tokenId, reason);
                }

                return { ...result, needSwitchToken: true };
            }

            // If possible network issue and not last attempt, try recovery
            if (result.possibleNetworkIssue && attempt < maxRetries) {
                this.logger?.debug(`🔄 Attempting recovery for network issue...`);

                try {
                    await page.reload({
                        waitUntil: 'networkidle',
                        timeout: this.config.pageTimeout || 100000
                    });
                    await page.waitForTimeout(2000);
                } catch (refreshError) {
                    this.logger?.warn(`Page refresh failed`, { error: refreshError.message });
                    if (attempt === maxRetries) {
                        return { ...result, needSwitchToken: true };
                    }
                }
            } else if (attempt < maxRetries) {
                await page.waitForTimeout(3000);
            }
        }

        this.logger?.error(`❌ All ${maxRetries} element check attempts failed`);
        return lastResult || {
            valid: false,
            reason: `Element check failed after ${maxRetries} attempts (${pageContext})`,
            needSwitchToken: true
        };
    }

    /**
     * Check page elements for validity
     * Integrated from original PlaywrightCrawler.js
     */
    async checkPageElements(page) {
        try {
            // Check if redirected to login page
            const currentUrl = page.url();
            if (this.isLoginPage(currentUrl)) {
                return { valid: false, reason: `Redirected to login: ${currentUrl}`, isLoginRedirect: true };
            }

            // Check for key elements
            const elementsToCheck = [
                { selector: '#textbook_tree', name: 'Textbook tree container' },
                { selector: '.ant-dropdown-trigger', name: 'Dropdown trigger' },
                { selector: 'ul.ant-pagination', name: 'Pagination component' },
                { selector: 'input.pagination_jump_page_input', name: 'Page jump input' }
            ];

            const missingElements = [];
            const existingElements = [];

            for (const element of elementsToCheck) {
                try {
                    const count = await page.locator(element.selector).count();
                    if (count > 0) {
                        existingElements.push(element.name);
                    } else {
                        missingElements.push(element.name);
                    }
                } catch (error) {
                    missingElements.push(element.name);
                }
            }

            this.logger?.debug(`✅ Found elements: ${existingElements.join(', ')}`);
            if (missingElements.length > 0) {
                this.logger?.debug(`❌ Missing elements: ${missingElements.join(', ')}`);
            }

            // If most key elements are missing, likely a token/network issue
            if (missingElements.length >= 3) {
                return {
                    valid: false,
                    reason: `Missing key elements: ${missingElements.join(', ')}`,
                    missingElements: missingElements,
                    existingElements: existingElements,
                    possibleNetworkIssue: true
                };
            }

            // Check for error elements
            const errorSelectors = [
                '.ant-result-error',
                '.error-page',
                '.login-form',
                '.ant-empty',
                '[class*="error"]',
                '[class*="forbidden"]'
            ];

            for (const selector of errorSelectors) {
                try {
                    const count = await page.locator(selector).count();
                    if (count > 0) {
                        return {
                            valid: false,
                            reason: `Error element detected: ${selector}`,
                            errorElement: selector,
                            isTokenError: true
                        };
                    }
                } catch (error) {
                    // Ignore check errors
                }
            }

            return { valid: true, reason: 'Page elements check passed' };

        } catch (error) {
            return {
                valid: false,
                reason: `Element check exception: ${error.message}`,
                error: error.message,
                possibleNetworkIssue: true
            };
        }
    }

    /**
     * Check if current page is a login page
     */
    isLoginPage(url) {
        return url.includes('login') ||
               url.includes('auth') ||
               /https:\/\/www\.stzy\.com\/login\//.test(url);
    }

    /**
     * Get random delay between min and max
     */
    getRandomDelay() {
        return Math.floor(Math.random() * (this.config.maxDelay - this.config.minDelay + 1)) + this.config.minDelay;
    }

    /**
     * Sleep for specified milliseconds
     */
    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    /**
     * Update performance metrics
     */
    updateMetrics(success, duration) {
        this.metrics.totalRequests++;
        
        if (success) {
            this.metrics.successfulRequests++;
            this.successCount++;
        } else {
            this.metrics.failedRequests++;
            this.errorCount++;
        }

        this.requestCount++;

        // Track response times
        this.metrics.responseTimes.push(duration);
        if (this.metrics.responseTimes.length > 100) {
            this.metrics.responseTimes = this.metrics.responseTimes.slice(-100);
        }

        // Calculate average response time
        if (this.metrics.responseTimes.length > 0) {
            this.metrics.averageResponseTime = this.metrics.responseTimes.reduce((a, b) => a + b, 0) / this.metrics.responseTimes.length;
        }
    }

    /**
     * Close specific page
     */
    async closePage(pageId) {
        try {
            const pageInfo = this.pages.get(pageId);
            if (pageInfo) {
                await pageInfo.page.close();
                this.pages.delete(pageId);
                this.logger?.debug('📃 Page closed', { pageId });
            }
        } catch (error) {
            this.logger?.warn('❌ Failed to close page', { pageId, error: error.message });
        }
    }

    /**
     * Close specific context
     */
    async closeContext(contextId) {
        try {
            const context = this.contexts.get(contextId);
            if (context) {
                await context.close();
                this.contexts.delete(contextId);
                this.logger?.debug('📄 Context closed', { contextId });
            }
        } catch (error) {
            this.logger?.warn('❌ Failed to close context', { contextId, error: error.message });
        }
    }

    /**
     * Get crawler statistics
     */
    getStatistics() {
        return {
            ...this.metrics,
            isInitialized: this.isInitialized,
            isShuttingDown: this.isShuttingDown,
            activePages: this.pages.size,
            activeContexts: this.contexts.size,
            successRate: this.requestCount > 0 ? (this.successCount / this.requestCount) * 100 : 0,
            errorRate: this.requestCount > 0 ? (this.errorCount / this.requestCount) * 100 : 0
        };
    }

    /**
     * Shutdown crawler and cleanup resources
     */
    async shutdown() {
        if (this.isShuttingDown) return;
        this.isShuttingDown = true;

        this.logger?.info('🔄 Shutting down Enhanced Playwright Crawler');

        try {
            // Close all pages
            for (const pageId of this.pages.keys()) {
                await this.closePage(pageId);
            }

            // Close all contexts
            for (const contextId of this.contexts.keys()) {
                await this.closeContext(contextId);
            }

            // Close browser
            if (this.browser) {
                await this.browser.close();
                this.browser = null;
            }

            this.isInitialized = false;
            this.logger?.info('✅ Enhanced Playwright Crawler shutdown completed');

        } catch (error) {
            this.logger?.error('❌ Error during crawler shutdown', { error: error.message });
        }
    }

    /**
     * Check loading state of the page
     * Integrated from original PlaywrightCrawler.js
     */
    async checkLoadingState(page, timeoutMs = 10000) {
        try {
            this.logger?.debug('🔍 Checking page loading state');

            const startTime = Date.now();
            let isLoading = false;

            // Check for loading images
            const checkLoading = async () => {
                try {
                    const images = await page.locator('img.w80.h30').all();

                    for (const img of images) {
                        const src = await img.getAttribute('src');
                        const isVisible = await img.isVisible();

                        if (src === 'https://zj.stzy.com/images/create-paper/loading.gif' && isVisible) {
                            return { isLoading: true, element: img };
                        }
                    }

                    return { isLoading: false, element: null };
                } catch (error) {
                    return { isLoading: false, element: null };
                }
            };

            const initialCheck = await checkLoading();
            if (!initialCheck.isLoading) {
                return {
                    isLoading: false,
                    reason: 'No loading state detected',
                    needAction: false
                };
            }

            this.logger?.debug('⏳ Loading detected, monitoring...');
            isLoading = true;

            // Monitor loading state
            while (isLoading && (Date.now() - startTime) < timeoutMs) {
                await page.waitForTimeout(1000);

                const currentCheck = await checkLoading();
                if (!currentCheck.isLoading) {
                    const duration = Date.now() - startTime;
                    this.logger?.debug(`✅ Loading completed in ${duration}ms`);
                    return {
                        isLoading: false,
                        reason: 'Loading completed',
                        duration: duration,
                        needAction: false
                    };
                }
            }

            if (isLoading) {
                const duration = Date.now() - startTime;
                this.logger?.warn(`⚠️ Loading timeout after ${duration}ms`);

                return {
                    isLoading: true,
                    reason: 'Loading timeout',
                    duration: duration,
                    needAction: true
                };
            }

            return {
                isLoading: false,
                reason: 'Check completed',
                needAction: false
            };

        } catch (error) {
            this.logger?.warn('Error checking loading state', { error: error.message });
            return {
                isLoading: false,
                reason: `Check error: ${error.message}`,
                needAction: false
            };
        }
    }

    /**
     * Handle loading timeout situations
     * Integrated from original PlaywrightCrawler.js
     */
    async handleLoadingTimeout(page, pageContext = '') {
        try {
            this.logger?.warn(`🔄 Handling loading timeout for ${pageContext}`);

            // Check if it's a token issue
            const tokenCheck = await this.checkTokenStatus(page);

            if (tokenCheck.isTokenBanned) {
                this.logger?.error(`🚫 Token issue detected: ${tokenCheck.reason}`);

                // Mark current token as permanently invalid if available
                const currentToken = this.getCurrentToken();
                if (currentToken && this.tokenManager) {
                    this.tokenManager.markTokenPermanentlyInvalid(currentToken.tokenId, tokenCheck.reason);
                }

                return {
                    action: 'switchToken',
                    reason: tokenCheck.reason,
                    needSwitchToken: true
                };
            }

            // Try refreshing the page
            this.logger?.debug('🔄 Attempting page refresh...');

            try {
                await page.reload({
                    waitUntil: 'networkidle',
                    timeout: this.config.pageTimeout || 100000
                });

                await page.waitForTimeout(3000);

                // Check loading state again
                const loadingCheck = await this.checkLoadingState(page, 5000);

                if (loadingCheck.needAction) {
                    this.logger?.warn('⚠️ Still loading after refresh, may be token issue');
                    return {
                        action: 'switchToken',
                        reason: 'Still loading after refresh',
                        needSwitchToken: true
                    };
                }

                this.logger?.info('✅ Page refresh successful');
                return {
                    action: 'reconfigure',
                    reason: 'Page refresh successful',
                    needReconfigure: true
                };

            } catch (refreshError) {
                this.logger?.error('❌ Page refresh failed', { error: refreshError.message });
                return {
                    action: 'switchToken',
                    reason: `Page refresh failed: ${refreshError.message}`,
                    needSwitchToken: true
                };
            }

        } catch (error) {
            this.logger?.error('❌ Error handling loading timeout', { error: error.message });
            return {
                action: 'switchToken',
                reason: `Timeout handling failed: ${error.message}`,
                needSwitchToken: true
            };
        }
    }

    /**
     * Check token status
     * Integrated from original PlaywrightCrawler.js
     */
    async checkTokenStatus(page) {
        try {
            // Check current URL for login redirect
            const currentUrl = page.url();
            if (this.isLoginPage(currentUrl)) {
                return {
                    isTokenBanned: true,
                    reason: `Page redirected to login: ${currentUrl}`
                };
            }

            // Check for error elements
            const errorSelectors = [
                '.ant-result-error',
                '.error-page',
                '.login-form',
                '.ant-empty',
                '[class*="error"]',
                '[class*="forbidden"]',
                '[class*="expired"]'
            ];

            for (const selector of errorSelectors) {
                try {
                    const errorElement = page.locator(selector);
                    const count = await errorElement.count();
                    if (count > 0) {
                        const isVisible = await errorElement.first().isVisible();
                        if (isVisible) {
                            return {
                                isTokenBanned: true,
                                reason: `Error element visible: ${selector}`
                            };
                        }
                    }
                } catch (error) {
                    // Ignore individual selector errors
                }
            }

            // Check for missing key elements
            const keyElements = [
                '#textbook_tree',
                '.ant-dropdown-trigger',
                'ul.ant-pagination'
            ];

            let missingCount = 0;
            for (const selector of keyElements) {
                try {
                    const count = await page.locator(selector).count();
                    if (count === 0) {
                        missingCount++;
                    }
                } catch (error) {
                    missingCount++;
                }
            }

            // If most key elements are missing, likely token issue
            if (missingCount >= 2) {
                return {
                    isTokenBanned: true,
                    reason: `Key elements missing (${missingCount}/${keyElements.length})`
                };
            }

            return {
                isTokenBanned: false,
                reason: 'Token status appears normal'
            };

        } catch (error) {
            return {
                isTokenBanned: false,
                reason: `Token status check error: ${error.message}`
            };
        }
    }

    /**
     * Generate output path for parameter combination
     * Integrated from original PlaywrightCrawler.js logic
     */
    generateOutputPath(parameters) {
        const pathComponents = [
            { original: parameters.studyPhaseName, encoded: this.encodeDirName(parameters.studyPhaseName) },
            { original: parameters.subjectName, encoded: this.encodeDirName(parameters.subjectName) },
            { original: parameters.textbookVersionName, encoded: this.encodeDirName(parameters.textbookVersionName) },
            { original: parameters.ceciName, encoded: this.encodeDirName(parameters.ceciName) },
            { original: parameters.catalogName, encoded: this.encodeDirName(parameters.catalogName) }
        ];

        const encodedPath = path.join(this.config.outputDir || './output', ...pathComponents.map(c => c.encoded));

        return {
            encodedPath: encodedPath,
            pathComponents: pathComponents,
            originalPath: pathComponents.map(c => c.original).join('/') // 用于显示和调试
        };
    }

    /**
     * Encode directory name for safe file system usage
     * Integrated from original PlaywrightCrawler.js
     */
    encodeDirName(name) {
        if (!name) return 'unknown';

        // 将中文和特殊字符进行URL编码
        const encoded = encodeURIComponent(name)
            .replace(/\./g, '%2E')  // 编码点号
            .replace(/!/g, '%21')   // 编码感叹号
            .replace(/'/g, '%27')   // 编码单引号
            .replace(/\(/g, '%28')  // 编码左括号
            .replace(/\)/g, '%29')  // 编码右括号
            .replace(/\*/g, '%2A'); // 编码星号

        return encoded;
    }

    /**
     * Decode directory name from encoded format
     * Integrated from original PlaywrightCrawler.js
     */
    decodeDirName(encodedName) {
        if (!encodedName) return 'unknown';

        try {
            const decoded = decodeURIComponent(encodedName);
            return decoded.trim();
        } catch (error) {
            console.warn(`⚠️ 解码目录名失败: ${encodedName}, 错误: ${error.message}`);
            return encodedName;
        }
    }

    /**
     * Navigate to page and configure settings
     */
    async navigateAndConfigure(page, baseURL, parameters) {
        this.logger?.debug('🔗 Navigating to base URL and configuring settings');

        await page.goto(baseURL, {
            waitUntil: 'networkidle',
            timeout: this.config.pageTimeout || 100000
        });

        // Wait for page to stabilize
        await page.waitForTimeout(2000);

        // Configure page settings based on parameters
        await this.configurePageSettings(page, parameters);
    }

    /**
     * Configure page settings (placeholder - implement based on original logic)
     */
    async configurePageSettings(page, parameters) {
        // This would contain the detailed setting configuration logic
        // from the original crawler's clickSettingElements method
        this.logger?.debug('⚙️ Configuring page settings', { parameters });

        // Add implementation based on original crawler logic
        // For now, just wait to ensure page is ready
        await page.waitForTimeout(1000);
    }

    /**
     * Navigate to specific page number
     */
    async navigateToPage(page, pageNumber) {
        this.logger?.debug(`📄 Navigating to page ${pageNumber}`);

        try {
            // Look for page input field and navigate
            const pageInput = page.locator('input.pagination_jump_page_input');
            if (await pageInput.count() > 0) {
                await pageInput.clear();
                await pageInput.fill(pageNumber.toString());
                await page.keyboard.press('Enter');
                await page.waitForTimeout(2000);
            }
        } catch (error) {
            this.logger?.warn(`Failed to navigate to page ${pageNumber}`, { error: error.message });
        }
    }

    /**
     * Save page data with complete information
     * Integrated from original PlaywrightCrawler.js
     */
    async savePageData(pageNum, list, fullResponse, requestData, pathInfo, parameters) {
        const filename = `${pageNum}.json`;
        const filepath = path.join(pathInfo.encodedPath, filename);

        // Generate complete data structure like original crawler
        const jsonData = {
            fullResponse: fullResponse,
            requestData: requestData, // 保存请求参数用于验证
            crawlInfo: {
                pageNum: pageNum,
                timestamp: new Date().toISOString(),
                combination: {
                    studyPhaseName: parameters.studyPhaseName,
                    subjectName: parameters.subjectName,
                    textbookVersionName: parameters.textbookVersionName,
                    ceciName: parameters.ceciName,
                    catalogName: parameters.catalogName
                },
                recordCount: list.length,
                // 添加路径信息，便于后续验证和调试
                pathInfo: {
                    encodedPath: pathInfo.encodedPath,
                    originalPath: pathInfo.originalPath,
                    pathComponents: pathInfo.pathComponents
                }
            }
        };

        return new Promise((resolve, reject) => {
            fs.writeFile(filepath, JSON.stringify(jsonData, null, 2), 'utf8', (err) => {
                if (err) {
                    this.logger?.error(`❌ Failed to save page ${pageNum} data`, { error: err.message });
                    reject(err);
                } else {
                    this.logger?.debug(`💾 Page ${pageNum} data saved successfully`, {
                        filepath,
                        recordCount: list.length
                    });
                    resolve();
                }
            });
        });
    }

    /**
     * Setup network listening for API response capture
     * Integrated from original PlaywrightCrawler.js
     */
    setupNetworkListening(page, pageNum) {
        let responsePromise = null;
        let responseResolver = null;
        let captured = false;

        responsePromise = new Promise((resolve, reject) => {
            responseResolver = resolve;

            const responseHandler = async (response) => {
                if (captured) return; // 已经捕获过了，忽略后续响应

                if (response.url().includes(this.config.apiURL || '/api/')) {
                    try {
                        // 获取请求信息
                        const request = response.request();
                        const requestData = request.postData();

                        if (requestData) {
                            try {
                                const requestJson = JSON.parse(requestData);

                                // 验证请求参数是否匹配当前组合
                                if (this.isRequestMatchingCurrentCombination(requestJson)) {
                                    const responseData = await response.json();

                                    // 检查响应中的403错误
                                    if (responseData && responseData.code === 403) {
                                        this.logger?.warn(`🚫 Page ${pageNum} detected 403 error, token may be banned`);
                                        captured = true;
                                        page.off('response', responseHandler);
                                        resolve({
                                            page: pageNum,
                                            url: response.url(),
                                            status: response.status(),
                                            data: responseData,
                                            requestData: requestJson,
                                            timestamp: new Date().toISOString(),
                                            tokenBanned: true
                                        });
                                        return;
                                    }

                                    captured = true;
                                    page.off('response', responseHandler); // 移除监听器

                                    resolve({
                                        page: pageNum,
                                        url: response.url(),
                                        status: response.status(),
                                        data: responseData,
                                        requestData: requestJson,
                                        timestamp: new Date().toISOString()
                                    });
                                }
                            } catch (parseError) {
                                this.logger?.warn(`⚠️ Failed to parse request data: ${parseError.message}`);
                            }
                        }
                    } catch (error) {
                        this.logger?.error(`❌ Failed to parse page ${pageNum} API response: ${error.message}`);
                    }
                }
            };

            page.on('response', responseHandler);

            // 设置超时，避免无限等待
            setTimeout(() => {
                if (!captured) {
                    page.off('response', responseHandler);
                    this.logger?.warn(`⏰ Page ${pageNum} response capture timeout, skipping`);
                    resolve({
                        page: pageNum,
                        url: null,
                        status: 408,
                        data: null,
                        requestData: null,
                        timestamp: new Date().toISOString(),
                        timeout: true
                    });
                }
            }, 30000); // 30秒超时
        });

        return responsePromise;
    }

    /**
     * Check if request matches current parameter combination
     * Integrated from original PlaywrightCrawler.js
     */
    isRequestMatchingCurrentCombination(requestJson) {
        try {
            // 基本的请求验证逻辑
            if (!requestJson || !requestJson.params) {
                return false;
            }

            // 这里可以添加更具体的参数匹配逻辑
            // 目前简化为检查是否有基本的参数结构
            return true;
        } catch (error) {
            this.logger?.warn(`⚠️ Failed to validate request matching: ${error.message}`);
            return false;
        }
    }

    /**
     * Get maximum page number from pagination component
     * Integrated from original PlaywrightCrawler.js
     */
    async getMaxPageFromPagination(page) {
        try {
            // 等待分页组件加载
            const pagination = page.locator('ul.ant-pagination');
            await pagination.waitFor({ state: 'visible', timeout: 30000 });

            // 查找所有有title属性的li元素
            const titleElements = await pagination.locator('li[title]').all();

            let maxPage = 1;
            for (const element of titleElements) {
                const titleValue = await element.getAttribute('title');
                if (titleValue) {
                    // 检查title值是否为纯数字
                    const pageNumber = parseInt(titleValue.trim());
                    if (!isNaN(pageNumber) && pageNumber > maxPage) {
                        maxPage = pageNumber;
                    }
                }
            }

            this.logger?.debug(`📄 Maximum pages detected: ${maxPage}`);
            return maxPage;

        } catch (error) {
            this.logger?.warn(`⚠️ Failed to get max page from pagination: ${error.message}`);
            return 1;
        }
    }

    /**
     * Handle pagination navigation to specific page
     * Integrated from original PlaywrightCrawler.js
     */
    async handlePagination(page, targetPage) {
        try {
            this.logger?.debug(`📄 Navigating to page ${targetPage}`);

            // Check if pagination input exists
            const pageInput = page.locator('input.pagination_jump_page_input');
            try {
                await pageInput.waitFor({ state: 'visible', timeout: 30000 });
            } catch (error) {
                this.logger?.error('❌ Pagination input not found');
                return { success: false, reason: 'Pagination input not found' };
            }

            // Clear and input target page number
            await pageInput.clear();
            await pageInput.fill(targetPage.toString());
            await page.keyboard.press('Enter');

            // Wait for page to load
            await page.waitForTimeout(3000);

            // Verify navigation success
            try {
                const activePage = page.locator('.ant-pagination-item-active');
                await activePage.waitFor({ state: 'visible', timeout: 10000 });
                const activePageText = await activePage.textContent();
                const activePageNum = parseInt(activePageText?.trim() || '0');

                if (activePageNum === targetPage) {
                    this.logger?.debug(`✅ Successfully navigated to page ${targetPage}`);
                    return { success: true, actualPage: activePageNum };
                } else {
                    this.logger?.warn(`⚠️ Navigation mismatch: expected ${targetPage}, got ${activePageNum}`);
                    return { success: false, reason: `Page mismatch: expected ${targetPage}, got ${activePageNum}` };
                }
            } catch (error) {
                this.logger?.warn(`⚠️ Could not verify page navigation: ${error.message}`);
                // Assume success if we can't verify
                return { success: true, actualPage: targetPage };
            }

        } catch (error) {
            this.logger?.error(`❌ Pagination navigation failed: ${error.message}`);
            return { success: false, reason: error.message };
        }
    }

    /**
     * Intelligent delay with randomization
     */
    async intelligentDelay() {
        const delay = this.getRandomDelay();
        this.logger?.debug(`⏱️ Applying intelligent delay: ${delay}ms`);
        await this.sleep(delay);
    }
}

module.exports = EnhancedPlaywrightCrawler;
